# Extension Configuration
# This file controls which extension provider to use for this project

# Extension type to use (cline, roo-code, custom-extension)
extension.type=cline

# Auto-switch enabled (true/false)
auto.switch=false

# Extension-specific settings
extension.cline.api_key=${CLINE_API_KEY}
extension.cline.model=gpt-4
extension.cline.temperature=0.7

extension.roo-code.api_key=${ROO_API_KEY}
extension.roo-code.endpoint=https://api.roo.ai
extension.roo-code.timeout=30000

# Debug mode (all, idea, release, none)
debug.mode=none

# Log level (debug, info, warn, error)
log.level=info